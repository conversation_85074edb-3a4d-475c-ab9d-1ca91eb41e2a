use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, WebviewUrl, WebviewWindowBuilder, WindowEvent};
use std::path::PathBuf;

#[tauri::command]
pub async fn open_isolated_browser(app: AppHandle, account_id: &str) -> Result<String, String> {
    let url = "https://www.goofish.com/search?q=%E7%9B%B8%E6%9C%BA&spm=a21ybx.search.searchInput.0";

    // 如果账户ID为空，使用默认窗口标签，不进行数据隔离
    let (window_label, title, use_isolation) = if account_id.trim().is_empty() {
        (
            "default_browser".to_string(),
            "闲鱼浏览器".to_string(),
            false,
        )
    } else {
        (
            format!("browser_{}", account_id),
            format!("闲鱼浏览器 - 账户: {}", account_id),
            true,
        )
    };

    let mut builder = WebviewWindowBuilder::new(
        &app,
        &window_label,
        WebviewUrl::External(url.parse().unwrap()),
    )
    .title(&title)
    .inner_size(1200.0, 800.0);

    // 只有在需要隔离时才设置数据目录/标识符
    if use_isolation {
        #[cfg(target_os = "windows")]
        {
            let data_dir = PathBuf::from(format!("browser_data_{}", account_id));
            builder = builder.data_directory(data_dir);
        }

        #[cfg(target_os = "macos")]
        {
            // 创建一个基于账户ID的16字节标识符
            let mut identifier = [0u8; 16];
            let account_bytes = account_id.as_bytes();
            let copy_len = std::cmp::min(account_bytes.len(), 16);
            identifier[..copy_len].copy_from_slice(&account_bytes[..copy_len]);
            builder = builder.data_store_identifier(identifier);
        }
    }

    let result_message = if use_isolation {
        format!("已为账户 {} 打开隔离浏览器窗口", account_id)
    } else {
        "已打开默认浏览器窗口（无数据隔离）".to_string()
    };

    match builder.build() {
        Ok(window) => {
            // 监听窗口关闭事件
            let window_clone = window.clone();
            window.clone().on_window_event(move |event| {
                if let WindowEvent::CloseRequested { api, .. } = event {
                    // 阻止窗口立即关闭
                    api.prevent_close();

                    println!(
                        "窗口 {} 即将关闭，正在获取 cookies...",
                        window_clone.label()
                    );

                    // 同步获取 cookies
                    match window_clone.cookies() {
                        Ok(cookies) => {
                            println!("=== 窗口 {} 的 Cookies ===", window_clone.label());
                            println!("总共获取到 {} 个 cookies", cookies.len());
                            for cookie in &cookies {
                                println!("Cookie: {} = {}", cookie.name(), cookie.value());
                                println!("  域名: {}", cookie.domain().unwrap_or("N/A"));
                                println!("  路径: {}", cookie.path().unwrap_or("N/A"));
                                println!("  安全: {}", cookie.secure().unwrap_or(false));
                                println!("  HttpOnly: {}", cookie.http_only().unwrap_or(false));
                                println!("  过期时间: {:?}", cookie.expires());
                                println!("  ---");
                            }
                            println!("========================");
                        }
                        Err(e) => {
                            println!("获取窗口 {} 的 cookies 失败: {}", window_clone.label(), e);
                        }
                    }

                    // cookies 获取完成，现在关闭窗口
                    println!(
                        "窗口 {} cookies 获取完成，正在关闭窗口",
                        window_clone.label()
                    );
                    let _ = window_clone.close();
                }
            });
            Ok(result_message)
        }
        Err(e) => Err(format!("打开浏览器失败: {}", e)),
    }
}
