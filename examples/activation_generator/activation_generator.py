#!/usr/bin/env python3
"""
Goldfish 激活码生成器
用于生成RSA签名的激活码，独立于主应用运行
"""

import json
import base64
import hashlib
import os
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding


class ActivationGenerator:
    def __init__(self, private_key_path: str = None, public_key_path: str = None):
        self.private_key = None
        self.public_key = None
        
        if private_key_path:
            self.load_private_key(private_key_path)
        if public_key_path:
            self.load_public_key(public_key_path)
    
    def generate_key_pair(self, key_size: int = 2048) -> tuple:
        """生成RSA密钥对"""
        print(f"🔑 生成 {key_size} 位 RSA 密钥对...")
        
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=key_size
        )
        public_key = private_key.public_key()
        
        self.private_key = private_key
        self.public_key = public_key
        
        return private_key, public_key
    
    def save_keys(self, private_key_path: str, public_key_path: str):
        """保存密钥对到文件"""
        if not self.private_key or not self.public_key:
            raise ValueError("密钥对未生成")
        
        # 保存私钥
        private_pem = self.private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        with open(private_key_path, 'wb') as f:
            f.write(private_pem)
        
        # 保存公钥
        public_pem = self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        with open(public_key_path, 'wb') as f:
            f.write(public_pem)
        
        print(f"✅ 私钥已保存到: {private_key_path}")
        print(f"✅ 公钥已保存到: {public_key_path}")
    
    def load_private_key(self, private_key_path: str):
        """加载私钥"""
        with open(private_key_path, 'rb') as f:
            self.private_key = serialization.load_pem_private_key(
                f.read(),
                password=None
            )
        print(f"✅ 私钥已加载: {private_key_path}")
    
    def load_public_key(self, public_key_path: str):
        """加载公钥"""
        with open(public_key_path, 'rb') as f:
            self.public_key = serialization.load_pem_public_key(f.read())
        print(f"✅ 公钥已加载: {public_key_path}")
    
    def generate_activation_code(self, device_fingerprint: str, user_id: str, 
                               expire_days: int = 365, features: list = None) -> str:
        """生成激活码"""
        if not self.private_key:
            raise ValueError("私钥未加载")
        
        if features is None:
            features = ["monitor", "notification"]
        
        now = datetime.now()
        expire_time = now + timedelta(days=expire_days)
        
        # 创建激活数据
        activation_data = {
            "device_fingerprint": device_fingerprint,
            "user_id": user_id,
            "expire_timestamp": int(expire_time.timestamp()),
            "issued_timestamp": int(now.timestamp()),
            "features": features
        }
        
        # 序列化为JSON
        json_data = json.dumps(activation_data, separators=(',', ':'))
        
        # 使用私钥签名（与Rust的BlindedSigningKey<Sha256>一致）
        # 注意：使用DIGEST_LENGTH作为盐长度，这与Rust的默认行为更匹配
        signature = self.private_key.sign(
            json_data.encode('utf-8'),
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.DIGEST_LENGTH  # 使用摘要长度作为盐长度
            ),
            hashes.SHA256()
        )
        
        # 创建签名数据结构
        signed_data = {
            "data": json_data,
            "signature": base64.b64encode(signature).decode('utf-8')
        }
        
        # Base64编码最终结果
        final_json = json.dumps(signed_data, separators=(',', ':'))
        activation_code = base64.b64encode(final_json.encode('utf-8')).decode('utf-8')
        
        print(f"✅ 激活码生成成功")
        print(f"📱 设备指纹: {device_fingerprint}")
        print(f"👤 用户ID: {user_id}")
        print(f"⏰ 过期时间: {expire_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")
        print(f"🎯 功能列表: {', '.join(features)}")
        
        return activation_code
    
    def verify_activation_code(self, activation_code: str, device_fingerprint: str) -> dict:
        """验证激活码"""
        if not self.public_key:
            raise ValueError("公钥未加载")
        
        try:
            # Base64解码
            decoded_data = base64.b64decode(activation_code)
            json_string = decoded_data.decode('utf-8')
            signed_data = json.loads(json_string)
            
            # 提取数据和签名
            data = signed_data['data']
            signature = base64.b64decode(signed_data['signature'])
            
            # 验证签名
            self.public_key.verify(
                signature,
                data.encode('utf-8'),
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.DIGEST_LENGTH  # 使用摘要长度作为盐长度
                ),
                hashes.SHA256()
            )
            
            # 解析激活数据
            activation_data = json.loads(data)
            
            # 验证设备指纹
            if activation_data['device_fingerprint'] != device_fingerprint:
                raise ValueError("设备指纹不匹配")
            
            # 验证过期时间
            now = datetime.now()
            expire_time = datetime.fromtimestamp(activation_data['expire_timestamp'])
            
            if now > expire_time:
                raise ValueError("激活码已过期")
            
            print(f"✅ 激活码验证成功")
            return activation_data
            
        except Exception as e:
            print(f"❌ 激活码验证失败: {e}")
            raise
    
    def get_public_key_fingerprint(self) -> str:
        """获取公钥指纹"""
        if not self.public_key:
            raise ValueError("公钥未加载")
        
        public_pem = self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        fingerprint = hashlib.sha256(public_pem).hexdigest()
        return fingerprint





# ==================== 配置区域 ====================
# 在这里直接配置参数，无需使用命令行

# 获取脚本文件所在目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

CONFIG = {
    # 操作模式 (选择一个，其他设为False)
    'generate_keys': False,      # 生成新的密钥对
    'generate_code': True,       # 生成激活码
    'verify_code': False,        # 验证激活码
    'show_fingerprint': False,   # 显示公钥指纹

    # 文件路径（相对于脚本文件）
    'private_key_path': os.path.join(SCRIPT_DIR, 'private_key.pem'),
    'public_key_path': os.path.join(SCRIPT_DIR, '..', '..', 'src-tauri', 'keys', 'public_key.pem'),

    # ########### 激活码生成参数 ###########
    # 'device_fingerprint': 'a519c7a5b2acca741765b444135c602aa6d38fa50e6a2c19d6f2039888a11d2d',  # 替换为实际的设备指纹
    # 'device_fingerprint': 'ca004d6505a8da3362651899ae805706136ee3c66d6228e727ad643cb97009b9',  # 替换为实际的设备指纹
    'device_fingerprint': '527abbd1ed210cffa1f8f743ddcb91a26645dc538efdf2539a9324f2d7b909aa',  # 替换为实际的设备指纹
    'user_id': 'user_001',               # 用户ID
    'expire_days': 20,                    # 过期天数
    'features': ['monitor', 'notification'],  # 功能列表
    # ########### 激活码生成参数 ###########

    # 验证参数
    'activation_code_to_verify': 'eyJkYXRhIjoie1wiZGV2aWNlX2ZpbmdlcnByaW50XCI6XCJhNTE5YzdhNWIyYWNjYTc0MTc2NWI0NDQxMzVjNjAyYWE2ZDM4ZmE1MGU2YTJjMTlkNmYyMDM5ODg4YTExZDJkXCIsXCJ1c2VyX2lkXCI6XCJ1c2VyXzAwMVwiLFwiZXhwaXJlX3RpbWVzdGFtcFwiOjE3NTI4MDQxMzcsXCJpc3N1ZWRfdGltZXN0YW1wXCI6MTc1MjcxNzczNyxcImZlYXR1cmVzXCI6W1wibW9uaXRvclwiLFwibm90aWZpY2F0aW9uXCJdfSIsInNpZ25hdHVyZSI6ImJScHZHb0RyZWkzYnVDS1d0c0Y4bHZDMHVFQUZRU0dOOXF0Vkxta1RzM2k0NGRoTHRZbUVrUnFZTVVIekhXZGd0N3NMSnF2bkVweS9lNEpTQnZOQWtpWEpmc0FYU2Vac0RXQlBHZHVXR3Z6cERvL1djWUhFelN1bUtHZmtwc2VuYXNqMG4zQ3VJdFBGOWpEKyt1eVI1aHB1WFIxd09UemJrQVNQNVhMaHBrTHpHMTZJajJvTnJFR2Rjc0NhbzVwdHJHZW9VenI1MUF0clM3N3RJQnN5YW41NzFwOURaN1pBZk16ZCtyWUlnOFlDQVZZbWg0YnFiYlFLVktPT2hETFRjTzJFUlVjTXNyN3FwUEcrcWcrZWo0MnJxVnhNUmJBWlI0c3FwWFV5eDluQkNHcXBHZjQvS0pYMEMzcFUvRmhMaFdtOEdXbUtnWCtiVHZXbUdPNGEwUT09In0=',     # 要验证的激活码
}

def run_with_config():
    """使用配置模式运行"""
    print("🔧 Goldfish 激活码生成器")
    print("=" * 50)

    generator = ActivationGenerator()

    # 生成密钥对
    if CONFIG['generate_keys']:
        print("🔑 生成密钥对...")
        generator.generate_key_pair()
        generator.save_keys(CONFIG['private_key_path'], CONFIG['public_key_path'])

        fingerprint = generator.get_public_key_fingerprint()
        print(f"🔍 公钥指纹: {fingerprint}")
        print(f"✅ 密钥对已保存:")
        print(f"   私钥: {CONFIG['private_key_path']}")
        print(f"   公钥: {CONFIG['public_key_path']}")

        # 提醒重新编译应用
        print("\n✅ 密钥生成完成!")
        print("💡 下一步操作:")
        print("   1. 重新编译应用 (应用会自动读取新的公钥文件)")
        print("   2. 启动应用: npm run tauri dev")
        print("   3. 使用新生成的激活码进行测试")

        print("\n💡 下次运行时请:")
        print("   1. 设置 generate_keys = False")
        print("   2. 设置 generate_code = True")
        print("   3. 填入正确的设备指纹")
        print("   4. 重新启动应用以使用新密钥")
        return

    # 显示公钥指纹
    if CONFIG['show_fingerprint']:
        try:
            generator.load_public_key(CONFIG['public_key_path'])
            fingerprint = generator.get_public_key_fingerprint()
            print(f"🔍 公钥指纹: {fingerprint}")
        except Exception as e:
            print(f"❌ 获取公钥指纹失败: {e}")
        return

    # 验证激活码
    if CONFIG['verify_code']:
        if not CONFIG['activation_code_to_verify']:
            print("❌ 请在配置中设置要验证的激活码")
            return
        if not CONFIG['device_fingerprint'] or CONFIG['device_fingerprint'] == '你的设备指纹':
            print("❌ 请在配置中设置正确的设备指纹")
            return

        try:
            generator.load_public_key(CONFIG['public_key_path'])
            result = generator.verify_activation_code(
                CONFIG['activation_code_to_verify'],
                CONFIG['device_fingerprint']
            )
            print(f"📋 激活信息:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ 验证失败: {e}")
        return

    # 生成激活码
    if CONFIG['generate_code']:
        if not CONFIG['device_fingerprint'] or CONFIG['device_fingerprint'] == '你的设备指纹':
            print("❌ 请在配置中设置正确的设备指纹")
            print("💡 提示: 在应用中可以获取设备指纹")
            return

        try:
            generator.load_private_key(CONFIG['private_key_path'])

            print(f"📋 生成激活码参数:")
            print(f"   设备指纹: {CONFIG['device_fingerprint']}")
            print(f"   用户ID: {CONFIG['user_id']}")
            print(f"   过期天数: {CONFIG['expire_days']}")
            print(f"   功能列表: {CONFIG['features']}")
            print()

            activation_code = generator.generate_activation_code(
                CONFIG['device_fingerprint'],
                CONFIG['user_id'],
                CONFIG['expire_days'],
                CONFIG['features']
            )

            print(f"🎫 生成的激活码:\n")
            print(f"{activation_code}\n")

        except Exception as e:
            print(f"❌ 生成激活码失败: {e}")
            if "No such file" in str(e):
                print("💡 提示: 请先生成密钥对 (设置 generate_keys = True)")
        return

    # 如果没有选择任何操作
    print("❌ 请在配置中选择一个操作模式")
    print("💡 可选操作:")
    print("   - generate_keys: 生成密钥对")
    print("   - generate_code: 生成激活码")
    print("   - verify_code: 验证激活码")
    print("   - show_fingerprint: 显示公钥指纹")


if __name__ == '__main__':
    # 直接使用配置模式
    run_with_config()
